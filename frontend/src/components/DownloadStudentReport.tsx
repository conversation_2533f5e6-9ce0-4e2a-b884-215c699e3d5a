import React, { useState } from 'react';
import { PDFDocument, rgb } from 'pdf-lib';
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';

// Type definitions
interface SubmissionData {
  studentName: string;
  rollNumber: string;
  totalMarks: number;
  maxMarks: number;
  percentage: number;
  pdfUrl?: string;
  detailedBreakdown: any;
  subjectName?: string;
}

interface DownloadStudentReportProps {
  submissionData: SubmissionData;
  breakdownContentRef: React.RefObject<HTMLDivElement>;
}

const DownloadStudentReport: React.FC<DownloadStudentReportProps> = ({ submissionData, breakdownContentRef }) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const downloadCombinedReport = async () => {
    console.log('Download button clicked!');
    console.log('Submission data:', submissionData);
    console.log('Breakdown ref current:', breakdownContentRef.current);

    if (!breakdownContentRef.current) {
      console.error('Breakdown content ref is not available');
      return;
    }

    setIsDownloading(true);
    console.log('Starting download process...');

    try {
      // Always create feedback-only PDF for now
      console.log('Creating feedback report PDF...');
      await createFeedbackOnlyPDF();
      console.log('Download completed successfully');
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Failed to generate PDF report. Please try again.');
    } finally {
      setIsDownloading(false);
      console.log('Download process finished');
    }
  };



  const createFeedbackOnlyPDF = async () => {
    try {
      console.log('Starting createFeedbackOnlyPDF...');
      console.log('Submission data:', submissionData);

      // Use the evaluation data directly (it's already parsed)
      console.log('Raw detailedBreakdown:', submissionData.detailedBreakdown);

      const evaluationData = submissionData.detailedBreakdown;
      console.log('Using evaluation data directly:', evaluationData);

      if (!evaluationData || !evaluationData.questions) {
        throw new Error('Unable to access evaluation data');
      }

      // Create new PDF document
      const pdfDoc = await PDFDocument.create();
      let currentPage = pdfDoc.addPage();
      const { width: pageWidth, height: pageHeight } = currentPage.getSize();
      let currentY = pageHeight - 50;

      const margin = 50;
      const lineHeight = 20;
      const sectionSpacing = 30;

      // Helper function to add new page if needed
      const checkPageSpace = (requiredSpace: number) => {
        if (currentY - requiredSpace < margin) {
          currentPage = pdfDoc.addPage();
          currentY = pageHeight - 50;
        }
      };

      // Helper function to draw text with word wrapping
      const drawWrappedText = (text: string, x: number, y: number, maxWidth: number, fontSize: number, color = rgb(0, 0, 0)) => {
        const words = text.split(' ');
        let line = '';
        let lineY = y;

        for (const word of words) {
          const testLine = line + (line ? ' ' : '') + word;
          const textWidth = testLine.length * (fontSize * 0.6); // Rough estimation

          if (textWidth > maxWidth && line) {
            currentPage.drawText(line, { x, y: lineY, size: fontSize, color });
            line = word;
            lineY -= lineHeight;
            checkPageSpace(lineHeight);
            if (lineY !== currentY) lineY = currentY;
          } else {
            line = testLine;
          }
        }

        if (line) {
          currentPage.drawText(line, { x, y: lineY, size: fontSize, color });
          return lineY - lineHeight;
        }
        return lineY;
      };

      // Add header
      currentPage.drawText('Detailed Feedback Report', {
        x: margin,
        y: currentY,
        size: 18,
        color: rgb(0, 0, 0)
      });
      currentY -= sectionSpacing;

      // Student information
      currentPage.drawText(`Student: ${submissionData.studentName}`, {
        x: margin,
        y: currentY,
        size: 14,
        color: rgb(0, 0, 0)
      });
      currentY -= lineHeight;

      currentPage.drawText(`Roll Number: ${submissionData.rollNumber}`, {
        x: margin,
        y: currentY,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      currentY -= lineHeight;

      currentPage.drawText(`Subject: ${submissionData.subjectName || 'N/A'}`, {
        x: margin,
        y: currentY,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      currentY -= lineHeight;

      currentPage.drawText(`Score: ${submissionData.totalMarks}/${submissionData.maxMarks} (${submissionData.percentage}%)`, {
        x: margin,
        y: currentY,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      currentY -= lineHeight;

      currentPage.drawText(`Date: ${new Date().toLocaleDateString()}`, {
        x: margin,
        y: currentY,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      currentY -= sectionSpacing;

      // Add questions breakdown
      evaluationData.questions.forEach((question) => {
        checkPageSpace(100); // Ensure space for question header

        // Question header
        currentPage.drawText(`Question ${question.questionNumber}`, {
          x: margin,
          y: currentY,
          size: 14,
          color: rgb(0, 0, 0)
        });

        currentPage.drawText(`${question.marksAwarded}/${question.marksPossible} marks (${question.percentage}%)`, {
          x: pageWidth - 200,
          y: currentY,
          size: 12,
          color: rgb(0.3, 0.3, 0.3)
        });
        currentY -= lineHeight + 5;

        // Marking criteria
        if (question.criteriaBreakdown.length > 0) {
          currentPage.drawText('Marking Criteria:', {
            x: margin + 20,
            y: currentY,
            size: 12,
            color: rgb(0, 0, 0)
          });
          currentY -= lineHeight;

          question.criteriaBreakdown.forEach(criterion => {
            checkPageSpace(lineHeight);
            const criterionText = `• ${criterion.criterion}: ${criterion.score}/${criterion.maxScore}`;
            currentY = drawWrappedText(criterionText, margin + 40, currentY, pageWidth - margin - 80, 10, rgb(0.2, 0.2, 0.2));
          });
          currentY -= 10;
        }

        // Feedback
        if (question.feedback) {
          checkPageSpace(lineHeight * 2);
          currentPage.drawText('Feedback:', {
            x: margin + 20,
            y: currentY,
            size: 12,
            color: rgb(0, 0, 0)
          });
          currentY -= lineHeight;

          currentY = drawWrappedText(question.feedback, margin + 40, currentY, pageWidth - margin - 80, 10, rgb(0.2, 0.2, 0.2));
        }

        currentY -= sectionSpacing;
      });

      // Save and download
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${submissionData.studentName.replace(/\s+/g, '_')}_Feedback_Report_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('PDF created and downloaded successfully');

    } catch (error) {
      console.error('Error creating feedback PDF:', error);
      throw error;
    }
  };

  return (
    <div className="download-report-container">
      <button
            onClick={downloadCombinedReport}
            disabled={isDownloading}
            className="flex items-center gap-1 lg:gap-2 px-2 lg:px-3 py-2 text-xs lg:text-sm font-medium text-foreground bg-muted hover:bg-accent border border-border rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download Student Report"
        >
            <ArrowDownTrayIcon className="w-3 lg:w-4 h-3 lg:h-4" />
            <span className="hidden sm:inline">{isDownloading ? 'Generating...' : 'Download Report'}</span>
        </button>
      
      {/* {submissionData.pdfUrl && (
        <p className="report-info">
          This will combine the original answer sheet with detailed feedback.
        </p>
      )} */}
    </div>
  );
};

export default DownloadStudentReport;
