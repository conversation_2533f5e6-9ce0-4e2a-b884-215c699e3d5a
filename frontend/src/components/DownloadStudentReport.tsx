import React, { useState } from 'react';
import { PDFDocument, rgb } from 'pdf-lib';
import html2canvas from 'html2canvas';
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';

// Type definitions
interface SubmissionData {
  studentName: string;
  rollNumber: string;
  totalMarks: number;
  maxMarks: number;
  percentage: number;
  pdfUrl?: string;
  detailedBreakdown: any;
  subjectName?: string;
}

interface DownloadStudentReportProps {
  submissionData: SubmissionData;
  breakdownContentRef: React.RefObject<HTMLDivElement>;
}

const DownloadStudentReport: React.FC<DownloadStudentReportProps> = ({ submissionData, breakdownContentRef }) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const downloadCombinedReport = async () => {
    console.log('Download button clicked!');
    console.log('Submission data:', submissionData);
    console.log('Breakdown ref current:', breakdownContentRef.current);

    if (!breakdownContentRef.current) {
      console.error('Breakdown content ref is not available');
      return;
    }

    setIsDownloading(true);
    console.log('Starting download process...');

    try {
      // Always create feedback-only PDF for now
      console.log('Creating feedback report PDF...');
      await createFeedbackOnlyPDF();
      console.log('Download completed successfully');
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Failed to generate PDF report. Please try again.');
    } finally {
      setIsDownloading(false);
      console.log('Download process finished');
    }
  };



  const createFeedbackOnlyPDF = async () => {
    try {
      console.log('Starting createFeedbackOnlyPDF...');

      // Check if breakdownContentRef is available
      if (!breakdownContentRef.current) {
        throw new Error('Breakdown content ref is not available');
      }

      console.log('Creating new PDF document...');
      // Create new PDF document
      const pdfDoc = await PDFDocument.create();
      const page = pdfDoc.addPage();
      const { width: pageWidth, height: pageHeight } = page.getSize();
      console.log('PDF document created, page size:', pageWidth, 'x', pageHeight);

      // Capture feedback content
      console.log('Starting html2canvas capture for feedback-only PDF...');
      console.log('Element dimensions:', {
        scrollWidth: breakdownContentRef.current.scrollWidth,
        scrollHeight: breakdownContentRef.current.scrollHeight,
        offsetWidth: breakdownContentRef.current.offsetWidth,
        offsetHeight: breakdownContentRef.current.offsetHeight
      });

      let canvas;
      try {
        canvas = await html2canvas(breakdownContentRef.current, {
          scale: 1.0, // Reduced scale to avoid memory issues
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          ignoreElements: (element) => {
            // Skip elements that might have unsupported CSS
            const computedStyle = window.getComputedStyle(element);
            return computedStyle.color?.includes('oklab') ||
                   computedStyle.backgroundColor?.includes('oklab') ||
                   element.tagName === 'SCRIPT' ||
                   element.tagName === 'STYLE';
          },
          onclone: (clonedDoc) => {
            // Remove any CSS that might contain oklab and force basic styling
            const styles = clonedDoc.querySelectorAll('style, link[rel="stylesheet"]');
            styles.forEach(style => {
              if (style.textContent?.includes('oklab')) {
                style.remove();
              }
            });

            // Add basic fallback styles
            const fallbackStyle = clonedDoc.createElement('style');
            fallbackStyle.textContent = `
              * {
                color: #000000 !important;
                background-color: #ffffff !important;
                border-color: #cccccc !important;
              }
            `;
            clonedDoc.head.appendChild(fallbackStyle);
          }
        });
      } catch (canvasError) {
        console.error('html2canvas failed with advanced options, trying basic options:', canvasError);
        // Fallback with minimal options
        canvas = await html2canvas(breakdownContentRef.current, {
          scale: 1.0,
          backgroundColor: '#ffffff',
          logging: false,
          useCORS: false
        });
      }
      console.log('html2canvas completed for feedback-only PDF, canvas size:', canvas.width, 'x', canvas.height);
      
      const imgData = canvas.toDataURL('image/png');
      const response = await fetch(imgData);
      const imageBytes = await response.arrayBuffer();
      const pngImage = await pdfDoc.embedPng(imageBytes);
      
      // Add header
      page.drawText(`Feedback Report - ${submissionData.studentName}`, {
        x: 50,
        y: pageHeight - 50,
        size: 16,
        color: rgb(0, 0, 0)
      });
      
      // Add image
      const maxWidth = pageWidth - 100;
      const maxHeight = pageHeight - 150;
      
      const imgWidth = Math.min(maxWidth, pngImage.width);
      const imgHeight = (pngImage.height * imgWidth) / pngImage.width;
      const finalImgHeight = Math.min(imgHeight, maxHeight);
      const finalImgWidth = (imgWidth * finalImgHeight) / imgHeight;
      
      page.drawImage(pngImage, {
        x: 50,
        y: pageHeight - 100 - finalImgHeight,
        width: finalImgWidth,
        height: finalImgHeight
      });
      
      // Save and download
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${submissionData.studentName.replace(/\s+/g, '_')}_Feedback_Report_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error creating feedback PDF:', error);
      throw error;
    }
  };

  return (
    <div className="download-report-container">
      <button
            onClick={downloadCombinedReport}
            disabled={isDownloading}
            className="flex items-center gap-1 lg:gap-2 px-2 lg:px-3 py-2 text-xs lg:text-sm font-medium text-foreground bg-muted hover:bg-accent border border-border rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download Student Report"
        >
            <ArrowDownTrayIcon className="w-3 lg:w-4 h-3 lg:h-4" />
            <span className="hidden sm:inline">{isDownloading ? 'Generating...' : 'Download Report'}</span>
        </button>
      
      {/* {submissionData.pdfUrl && (
        <p className="report-info">
          This will combine the original answer sheet with detailed feedback.
        </p>
      )} */}
    </div>
  );
};

export default DownloadStudentReport;
