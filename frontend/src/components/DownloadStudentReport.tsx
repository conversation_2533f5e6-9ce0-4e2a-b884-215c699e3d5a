import React, { useState } from 'react';
import { PDFDocument, rgb } from 'pdf-lib';
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';

// Type definitions
interface CriterionBreakdown {
  criterion: string;
  score: string;
  maxScore?: string;
}

interface QuestionBreakdown {
  questionNumber: string;
  marksAwarded: number;
  marksPossible: number;
  percentage: number;
  feedback: string;
  criteriaBreakdown: CriterionBreakdown[];
}

interface EvaluationBreakdown {
  totalMarks: number;
  maxMarks: number;
  overallPercentage: number;
  questions: QuestionBreakdown[];
}

interface SubmissionData {
  studentName: string;
  rollNumber: string;
  totalMarks: number;
  maxMarks: number;
  percentage: number;
  pdfUrl?: string;
  detailedBreakdown: EvaluationBreakdown;
  subjectName?: string;
}

interface DownloadStudentReportProps {
  submissionData: SubmissionData;
  breakdownContentRef: React.RefObject<HTMLDivElement>;
}

const DownloadStudentReport: React.FC<DownloadStudentReportProps> = ({ submissionData, breakdownContentRef }) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const downloadCombinedReport = async () => {
    console.log('Download button clicked!');
    console.log('Submission data:', submissionData);
    console.log('Breakdown ref current:', breakdownContentRef.current);

    if (!breakdownContentRef.current) {
      console.error('Breakdown content ref is not available');
      return;
    }

    setIsDownloading(true);
    console.log('Starting download process...');

    try {
      // Always create feedback-only PDF for now
      console.log('Creating feedback report PDF...');
      await createFeedbackOnlyPDF();
      console.log('Download completed successfully');
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Failed to generate PDF report. Please try again.');
    } finally {
      setIsDownloading(false);
      console.log('Download process finished');
    }
  };



  const createFeedbackOnlyPDF = async () => {
    try {
      console.log('Starting createFeedbackOnlyPDF...');
      console.log('Submission data:', submissionData);

      // Use the evaluation data directly (it's already parsed)
      console.log('Raw detailedBreakdown:', submissionData.detailedBreakdown);

      const evaluationData = submissionData.detailedBreakdown;
      console.log('Using evaluation data directly:', evaluationData);

      if (!evaluationData || !evaluationData.questions) {
        throw new Error('Unable to access evaluation data');
      }

      // Create new PDF document with portrait orientation
      const pdfDoc = await PDFDocument.create();
      let currentPage = pdfDoc.addPage([595.28, 841.89]); // A4 portrait (width, height)
      const { width: pageWidth, height: pageHeight } = currentPage.getSize();
      let currentY = pageHeight - 50;

      const margin = 50;
      const lineHeight = 20;
      const sectionSpacing = 30;

      // Helper function to add new page if needed
      const checkPageSpace = (requiredSpace: number) => {
        if (currentY - requiredSpace < margin) {
          console.log('Creating new page, currentY:', currentY, 'requiredSpace:', requiredSpace);
          currentPage = pdfDoc.addPage([595.28, 841.89]); // A4 portrait
          currentY = pageHeight - 50;
          return true; // Indicate that a new page was created
        }
        return false;
      };

      // Helper function to draw text with word wrapping
      const drawWrappedText = (text: string, x: number, startY: number, maxWidth: number, fontSize: number, color = rgb(0, 0, 0)) => {
        // Clean the text first - remove any problematic characters
        const cleanText = text
          .replace(/[^\x20-\x7E\n\r\t]/g, '') // Remove non-printable characters except newlines, tabs
          .replace(/\s+/g, ' ') // Replace multiple spaces with single space
          .trim();

        if (!cleanText) {
          currentY -= lineHeight;
          return;
        }

        const words = cleanText.split(' ');
        let line = '';
        let currentLineY = startY;
        const charWidth = fontSize * 0.5; // Character width estimation

        for (let i = 0; i < words.length; i++) {
          const word = words[i];
          const testLine = line + (line ? ' ' : '') + word;
          const textWidth = testLine.length * charWidth;

          if (textWidth > maxWidth && line) {
            // Check if we need a new page before drawing
            if (currentLineY - lineHeight < margin) {
              checkPageSpace(lineHeight);
              currentLineY = currentY;
            }

            // Draw the current line
            try {
              currentPage.drawText(line, { x, y: currentLineY, size: fontSize, color });
            } catch (error) {
              console.error('Error drawing text:', line, error);
              currentPage.drawText(line.replace(/[^\x20-\x7E]/g, '?'), { x, y: currentLineY, size: fontSize, color });
            }

            // Move to next line
            currentLineY -= lineHeight;

            // Start new line with current word
            line = word;
          } else {
            line = testLine;
          }
        }

        // Draw the last line if there's content
        if (line) {
          // Check if we need a new page before drawing the last line
          if (currentLineY - lineHeight < margin) {
            checkPageSpace(lineHeight);
            currentLineY = currentY;
          }

          try {
            currentPage.drawText(line, { x, y: currentLineY, size: fontSize, color });
          } catch (error) {
            console.error('Error drawing final text:', line, error);
            currentPage.drawText(line.replace(/[^\x20-\x7E]/g, '?'), { x, y: currentLineY, size: fontSize, color });
          }
          currentLineY -= lineHeight;
        }

        // Update the global currentY to the last line position
        currentY = currentLineY;
      };

      // Add header
      currentPage.drawText('Detailed Feedback Report', {
        x: margin,
        y: currentY,
        size: 18,
        color: rgb(0, 0, 0)
      });
      currentY -= sectionSpacing;

      // Student information
      currentPage.drawText(`Student: ${submissionData.studentName}`, {
        x: margin,
        y: currentY,
        size: 14,
        color: rgb(0, 0, 0)
      });
      currentY -= lineHeight;

      currentPage.drawText(`Roll Number: ${submissionData.rollNumber}`, {
        x: margin,
        y: currentY,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      currentY -= lineHeight;

      currentPage.drawText(`Subject: ${submissionData.subjectName || 'N/A'}`, {
        x: margin,
        y: currentY,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      currentY -= lineHeight;

      currentPage.drawText(`Score: ${submissionData.totalMarks}/${submissionData.maxMarks} (${submissionData.percentage}%)`, {
        x: margin,
        y: currentY,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      currentY -= lineHeight;

      currentPage.drawText(`Date: ${new Date().toLocaleDateString()}`, {
        x: margin,
        y: currentY,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      currentY -= sectionSpacing;

      // Add questions breakdown
      evaluationData.questions.forEach((question) => {
        // Ensure space for question header (check for more space to avoid orphaned headers)
        checkPageSpace(lineHeight * 4);

        // Question header
        currentPage.drawText(`Question ${question.questionNumber}`, {
          x: margin,
          y: currentY,
          size: 14,
          color: rgb(0, 0, 0)
        });

        currentPage.drawText(`${question.marksAwarded}/${question.marksPossible} marks (${question.percentage}%)`, {
          x: pageWidth - 200,
          y: currentY,
          size: 12,
          color: rgb(0.3, 0.3, 0.3)
        });
        currentY -= lineHeight + 5;

        // Marking criteria
        if (question.criteriaBreakdown.length > 0) {
          checkPageSpace(lineHeight * 2);
          currentPage.drawText('Marking Criteria:', {
            x: margin + 20,
            y: currentY,
            size: 12,
            color: rgb(0, 0, 0)
          });
          currentY -= lineHeight;

          question.criteriaBreakdown.forEach(criterion => {
            checkPageSpace(lineHeight);
            const criterionText = `• ${criterion.criterion}: ${criterion.score}/${criterion.maxScore}`;
            drawWrappedText(criterionText, margin + 40, currentY, pageWidth - margin - 80, 10, rgb(0.2, 0.2, 0.2));
          });
          currentY -= 10;
        }

        // Feedback
        if (question.feedback) {
          console.log(`Question ${question.questionNumber} feedback:`, question.feedback);

          // Ensure space for feedback header
          checkPageSpace(lineHeight * 3);
          currentPage.drawText('Feedback:', {
            x: margin + 20,
            y: currentY,
            size: 12,
            color: rgb(0, 0, 0)
          });
          currentY -= lineHeight;

          // Draw feedback with proper wrapping
          drawWrappedText(question.feedback, margin + 40, currentY, pageWidth - margin - 80, 10, rgb(0.2, 0.2, 0.2));
        }

        // Add spacing between questions
        currentY -= sectionSpacing;

        // Ensure we don't start a new question too close to the bottom
        if (currentY < margin + 100) {
          checkPageSpace(100);
        }
      });

      // Save and download
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${submissionData.studentName.replace(/\s+/g, '_')}_Feedback_Report_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('PDF created and downloaded successfully');

    } catch (error) {
      console.error('Error creating feedback PDF:', error);
      throw error;
    }
  };

  return (
    <div className="download-report-container">
      <button
            onClick={downloadCombinedReport}
            disabled={isDownloading}
            className="flex items-center gap-1 lg:gap-2 px-2 lg:px-3 py-2 text-xs lg:text-sm font-medium text-foreground bg-muted hover:bg-accent border border-border rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download Student Report"
        >
            <ArrowDownTrayIcon className="w-3 lg:w-4 h-3 lg:h-4" />
            <span className="hidden sm:inline">{isDownloading ? 'Generating...' : 'Download Report'}</span>
        </button>
      
      {/* {submissionData.pdfUrl && (
        <p className="report-info">
          This will combine the original answer sheet with detailed feedback.
        </p>
      )} */}
    </div>
  );
};

export default DownloadStudentReport;
