import React, { useState } from 'react';
import { PDFDocument, rgb } from 'pdf-lib';
import html2canvas from 'html2canvas';
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';

// Type definitions
interface SubmissionData {
  studentName: string;
  rollNumber: string;
  totalMarks: number;
  maxMarks: number;
  percentage: number;
  pdfUrl?: string;
  detailedBreakdown: any;
  subjectName?: string;
}

interface DownloadStudentReportProps {
  submissionData: SubmissionData;
  breakdownContentRef: React.RefObject<HTMLDivElement>;
}

const DownloadStudentReport: React.FC<DownloadStudentReportProps> = ({ submissionData, breakdownContentRef }) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const downloadCombinedReport = async () => {
    if (!breakdownContentRef.current) {
      console.error('Breakdown content ref is not available');
      return;
    }

    setIsDownloading(true);
    
    try {
      if (submissionData.pdfUrl) {
        // Use PDF-lib to add feedback to existing PDF
        await addFeedbackToExistingPDF();
      } else {
        // Fallback to creating new PDF with feedback only
        await createFeedbackOnlyPDF();
      }
    } catch (error) {
      console.error('Error generating combined report:', error);
      // Fallback to basic feedback PDF
      await createFeedbackOnlyPDF();
    } finally {
      setIsDownloading(false);
    }
  };

  const addFeedbackToExistingPDF = async () => {
    try {
      // Check if pdfUrl exists and breakdownContentRef is available
      if (!submissionData.pdfUrl) {
        throw new Error('PDF URL is not available');
      }

      if (!breakdownContentRef.current) {
        throw new Error('Breakdown content ref is not available');
      }

      // Fetch existing PDF from S3
      const response = await fetch(submissionData.pdfUrl);
      if (!response.ok) {
        throw new Error('Failed to fetch PDF from S3');
      }

      const existingPdfBytes = await response.arrayBuffer();
      const pdfDoc = await PDFDocument.load(existingPdfBytes);

      // Capture feedback content as image
      const canvas = await html2canvas(breakdownContentRef.current, {
        scale: 1.5,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: breakdownContentRef.current.scrollWidth,
        height: breakdownContentRef.current.scrollHeight
      });
      
      // Convert canvas to PNG bytes
      const imgData = canvas.toDataURL('image/png');
      const response2 = await fetch(imgData);
      const imageBytes = await response2.arrayBuffer();
      
      // Embed image in PDF
      const pngImage = await pdfDoc.embedPng(imageBytes);
      
      // Add new page for feedback
      const feedbackPage = pdfDoc.addPage();
      const { width: pageWidth, height: pageHeight } = feedbackPage.getSize();
      
      // Add header
      feedbackPage.drawText(`Detailed Feedback - ${submissionData.studentName}`, {
        x: 50,
        y: pageHeight - 50,
        size: 16,
        color: rgb(0, 0, 0)
      });
      
      feedbackPage.drawText(`Subject: ${submissionData.subjectName || 'N/A'}`, {
        x: 50,
        y: pageHeight - 80,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      
      feedbackPage.drawText(`Date: ${new Date().toLocaleDateString()}`, {
        x: 50,
        y: pageHeight - 100,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      
      // Calculate image dimensions to fit page
      const maxWidth = pageWidth - 100;
      const maxHeight = pageHeight - 150;
      
      const imgWidth = Math.min(maxWidth, pngImage.width);
      const imgHeight = (pngImage.height * imgWidth) / pngImage.width;
      
      // If image is too tall, scale it down
      const finalImgHeight = Math.min(imgHeight, maxHeight);
      const finalImgWidth = (imgWidth * finalImgHeight) / imgHeight;
      
      // Center the image
      const xPos = (pageWidth - finalImgWidth) / 2;
      const yPos = pageHeight - 130 - finalImgHeight;
      
      feedbackPage.drawImage(pngImage, {
        x: xPos,
        y: yPos,
        width: finalImgWidth,
        height: finalImgHeight
      });
      
      // Save and download
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${submissionData.studentName.replace(/\s+/g, '_')}_Complete_Report_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error adding feedback to existing PDF:', error);
      throw error;
    }
  };

  const createFeedbackOnlyPDF = async () => {
    try {
      // Check if breakdownContentRef is available
      if (!breakdownContentRef.current) {
        throw new Error('Breakdown content ref is not available');
      }

      // Create new PDF document
      const pdfDoc = await PDFDocument.create();
      const page = pdfDoc.addPage();
      const { width: pageWidth, height: pageHeight } = page.getSize();

      // Capture feedback content
      const canvas = await html2canvas(breakdownContentRef.current, {
        scale: 1.5,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      });
      
      const imgData = canvas.toDataURL('image/png');
      const response = await fetch(imgData);
      const imageBytes = await response.arrayBuffer();
      const pngImage = await pdfDoc.embedPng(imageBytes);
      
      // Add header
      page.drawText(`Feedback Report - ${submissionData.studentName}`, {
        x: 50,
        y: pageHeight - 50,
        size: 16,
        color: rgb(0, 0, 0)
      });
      
      // Add image
      const maxWidth = pageWidth - 100;
      const maxHeight = pageHeight - 150;
      
      const imgWidth = Math.min(maxWidth, pngImage.width);
      const imgHeight = (pngImage.height * imgWidth) / pngImage.width;
      const finalImgHeight = Math.min(imgHeight, maxHeight);
      const finalImgWidth = (imgWidth * finalImgHeight) / imgHeight;
      
      page.drawImage(pngImage, {
        x: 50,
        y: pageHeight - 100 - finalImgHeight,
        width: finalImgWidth,
        height: finalImgHeight
      });
      
      // Save and download
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${submissionData.studentName.replace(/\s+/g, '_')}_Feedback_Report_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error creating feedback PDF:', error);
      throw error;
    }
  };

  return (
    <div className="download-report-container">
      <button
            onClick={downloadCombinedReport}
            disabled={isDownloading}
            className="flex items-center gap-1 lg:gap-2 px-2 lg:px-3 py-2 text-xs lg:text-sm font-medium text-foreground bg-muted hover:bg-accent border border-border rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download Student Report"
        >
            <ArrowDownTrayIcon className="w-3 lg:w-4 h-3 lg:h-4" />
            <span className="hidden sm:inline">{isDownloading ? 'Generating...' : 'Download'}</span>
        </button>
      
      {submissionData.pdfUrl && (
        <p className="report-info">
          This will combine the original answer sheet with detailed feedback.
        </p>
      )}
    </div>
  );
};

export default DownloadStudentReport;
