import React, { useState } from 'react';
import { PDFDocument, rgb } from 'pdf-lib';
import html2canvas from 'html2canvas';
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import { useS3Utils } from '@/hooks/useS3Utils';

// Type definitions
interface SubmissionData {
  studentName: string;
  rollNumber: string;
  totalMarks: number;
  maxMarks: number;
  percentage: number;
  pdfUrl?: string;
  detailedBreakdown: any;
  subjectName?: string;
}

interface DownloadStudentReportProps {
  submissionData: SubmissionData;
  breakdownContentRef: React.RefObject<HTMLDivElement>;
}

const DownloadStudentReport: React.FC<DownloadStudentReportProps> = ({ submissionData, breakdownContentRef }) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const { getPresignedUrl } = useS3Utils();

  const downloadCombinedReport = async () => {
    console.log('Download button clicked!');
    console.log('Submission data:', submissionData);
    console.log('Breakdown ref current:', breakdownContentRef.current);

    if (!breakdownContentRef.current) {
      console.error('Breakdown content ref is not available');
      return;
    }

    setIsDownloading(true);
    console.log('Starting download process...');

    try {
      if (submissionData.pdfUrl) {
        console.log('Using existing PDF path with pdfUrl:', submissionData.pdfUrl);
        // Use PDF-lib to add feedback to existing PDF
        await addFeedbackToExistingPDF();
      } else {
        console.log('Creating feedback-only PDF (no pdfUrl)');
        // Fallback to creating new PDF with feedback only
        await createFeedbackOnlyPDF();
      }
      console.log('Download completed successfully');
    } catch (error) {
      console.error('Error generating combined report:', error);
      console.log('Falling back to feedback-only PDF');
      // Fallback to basic feedback PDF
      try {
        await createFeedbackOnlyPDF();
        console.log('Fallback PDF created successfully');
      } catch (fallbackError) {
        console.error('Fallback PDF creation also failed:', fallbackError);
      }
    } finally {
      setIsDownloading(false);
      console.log('Download process finished');
    }
  };

  const addFeedbackToExistingPDF = async () => {
    try {
      console.log('Starting addFeedbackToExistingPDF...');

      // Check if pdfUrl exists and breakdownContentRef is available
      if (!submissionData.pdfUrl) {
        throw new Error('PDF URL is not available');
      }

      if (!breakdownContentRef.current) {
        throw new Error('Breakdown content ref is not available');
      }

      console.log('Getting presigned URL for S3 key:', submissionData.pdfUrl);
      // Get presigned URL for the S3 key
      const presignedUrl = await getPresignedUrl(submissionData.pdfUrl);
      console.log('Presigned URL obtained:', presignedUrl);

      // Fetch existing PDF from S3 using presigned URL
      const response = await fetch(presignedUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch PDF from S3: ${response.status} ${response.statusText}`);
      }
      console.log('PDF fetched successfully');

      const existingPdfBytes = await response.arrayBuffer();
      console.log('PDF loaded, size:', existingPdfBytes.byteLength, 'bytes');

      // Check if the response is actually a PDF
      const uint8Array = new Uint8Array(existingPdfBytes);
      const headerBytes = uint8Array.slice(0, 4);
      const pdfHeader = String.fromCharCode(headerBytes[0], headerBytes[1], headerBytes[2], headerBytes[3]);
      console.log('PDF header check:', pdfHeader);

      if (pdfHeader !== '%PDF') {
        console.error('Invalid PDF file - no PDF header found');
        // Convert response to text to see what we actually got
        const textContent = new TextDecoder().decode(existingPdfBytes);
        console.log('Response content (first 500 chars):', textContent.substring(0, 500));
        throw new Error('Invalid PDF file - no PDF header found');
      }

      const pdfDoc = await PDFDocument.load(existingPdfBytes);
      console.log('PDF document loaded successfully');

      // Capture feedback content as image
      console.log('Starting html2canvas capture...');
      console.log('Element dimensions:', {
        scrollWidth: breakdownContentRef.current.scrollWidth,
        scrollHeight: breakdownContentRef.current.scrollHeight,
        offsetWidth: breakdownContentRef.current.offsetWidth,
        offsetHeight: breakdownContentRef.current.offsetHeight
      });

      let canvas;
      try {
        canvas = await html2canvas(breakdownContentRef.current, {
          scale: 1.0, // Reduced scale to avoid memory issues
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          width: breakdownContentRef.current.scrollWidth,
          height: breakdownContentRef.current.scrollHeight,
          ignoreElements: (element) => {
            // Skip elements that might have unsupported CSS
            const computedStyle = window.getComputedStyle(element);
            return computedStyle.color?.includes('oklab') ||
                   computedStyle.backgroundColor?.includes('oklab') ||
                   element.tagName === 'SCRIPT' ||
                   element.tagName === 'STYLE';
          },
          onclone: (clonedDoc) => {
            // Remove any CSS that might contain oklab and force basic styling
            const styles = clonedDoc.querySelectorAll('style, link[rel="stylesheet"]');
            styles.forEach(style => {
              if (style.textContent?.includes('oklab')) {
                style.remove();
              }
            });

            // Add basic fallback styles
            const fallbackStyle = clonedDoc.createElement('style');
            fallbackStyle.textContent = `
              * {
                color: #000000 !important;
                background-color: #ffffff !important;
                border-color: #cccccc !important;
              }
            `;
            clonedDoc.head.appendChild(fallbackStyle);
          }
        });
      } catch (canvasError) {
        console.error('html2canvas failed with advanced options, trying basic options:', canvasError);
        // Fallback with minimal options
        canvas = await html2canvas(breakdownContentRef.current, {
          scale: 1.0,
          backgroundColor: '#ffffff',
          logging: false,
          useCORS: false
        });
      }
      console.log('html2canvas completed, canvas size:', canvas.width, 'x', canvas.height);
      
      // Convert canvas to PNG bytes
      const imgData = canvas.toDataURL('image/png');
      const response2 = await fetch(imgData);
      const imageBytes = await response2.arrayBuffer();
      
      // Embed image in PDF
      const pngImage = await pdfDoc.embedPng(imageBytes);
      
      // Add new page for feedback
      const feedbackPage = pdfDoc.addPage();
      const { width: pageWidth, height: pageHeight } = feedbackPage.getSize();
      
      // Add header
      feedbackPage.drawText(`Detailed Feedback - ${submissionData.studentName}`, {
        x: 50,
        y: pageHeight - 50,
        size: 16,
        color: rgb(0, 0, 0)
      });
      
      feedbackPage.drawText(`Subject: ${submissionData.subjectName || 'N/A'}`, {
        x: 50,
        y: pageHeight - 80,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      
      feedbackPage.drawText(`Date: ${new Date().toLocaleDateString()}`, {
        x: 50,
        y: pageHeight - 100,
        size: 12,
        color: rgb(0.3, 0.3, 0.3)
      });
      
      // Calculate image dimensions to fit page
      const maxWidth = pageWidth - 100;
      const maxHeight = pageHeight - 150;
      
      const imgWidth = Math.min(maxWidth, pngImage.width);
      const imgHeight = (pngImage.height * imgWidth) / pngImage.width;
      
      // If image is too tall, scale it down
      const finalImgHeight = Math.min(imgHeight, maxHeight);
      const finalImgWidth = (imgWidth * finalImgHeight) / imgHeight;
      
      // Center the image
      const xPos = (pageWidth - finalImgWidth) / 2;
      const yPos = pageHeight - 130 - finalImgHeight;
      
      feedbackPage.drawImage(pngImage, {
        x: xPos,
        y: yPos,
        width: finalImgWidth,
        height: finalImgHeight
      });
      
      // Save and download
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${submissionData.studentName.replace(/\s+/g, '_')}_Complete_Report_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error adding feedback to existing PDF:', error);
      throw error;
    }
  };

  const createFeedbackOnlyPDF = async () => {
    try {
      console.log('Starting createFeedbackOnlyPDF...');

      // Check if breakdownContentRef is available
      if (!breakdownContentRef.current) {
        throw new Error('Breakdown content ref is not available');
      }

      console.log('Creating new PDF document...');
      // Create new PDF document
      const pdfDoc = await PDFDocument.create();
      const page = pdfDoc.addPage();
      const { width: pageWidth, height: pageHeight } = page.getSize();
      console.log('PDF document created, page size:', pageWidth, 'x', pageHeight);

      // Capture feedback content
      console.log('Starting html2canvas capture for feedback-only PDF...');
      console.log('Element dimensions:', {
        scrollWidth: breakdownContentRef.current.scrollWidth,
        scrollHeight: breakdownContentRef.current.scrollHeight,
        offsetWidth: breakdownContentRef.current.offsetWidth,
        offsetHeight: breakdownContentRef.current.offsetHeight
      });

      let canvas;
      try {
        canvas = await html2canvas(breakdownContentRef.current, {
          scale: 1.0, // Reduced scale to avoid memory issues
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          ignoreElements: (element) => {
            // Skip elements that might have unsupported CSS
            const computedStyle = window.getComputedStyle(element);
            return computedStyle.color?.includes('oklab') ||
                   computedStyle.backgroundColor?.includes('oklab') ||
                   element.tagName === 'SCRIPT' ||
                   element.tagName === 'STYLE';
          },
          onclone: (clonedDoc) => {
            // Remove any CSS that might contain oklab and force basic styling
            const styles = clonedDoc.querySelectorAll('style, link[rel="stylesheet"]');
            styles.forEach(style => {
              if (style.textContent?.includes('oklab')) {
                style.remove();
              }
            });

            // Add basic fallback styles
            const fallbackStyle = clonedDoc.createElement('style');
            fallbackStyle.textContent = `
              * {
                color: #000000 !important;
                background-color: #ffffff !important;
                border-color: #cccccc !important;
              }
            `;
            clonedDoc.head.appendChild(fallbackStyle);
          }
        });
      } catch (canvasError) {
        console.error('html2canvas failed with advanced options, trying basic options:', canvasError);
        // Fallback with minimal options
        canvas = await html2canvas(breakdownContentRef.current, {
          scale: 1.0,
          backgroundColor: '#ffffff',
          logging: false,
          useCORS: false
        });
      }
      console.log('html2canvas completed for feedback-only PDF, canvas size:', canvas.width, 'x', canvas.height);
      
      const imgData = canvas.toDataURL('image/png');
      const response = await fetch(imgData);
      const imageBytes = await response.arrayBuffer();
      const pngImage = await pdfDoc.embedPng(imageBytes);
      
      // Add header
      page.drawText(`Feedback Report - ${submissionData.studentName}`, {
        x: 50,
        y: pageHeight - 50,
        size: 16,
        color: rgb(0, 0, 0)
      });
      
      // Add image
      const maxWidth = pageWidth - 100;
      const maxHeight = pageHeight - 150;
      
      const imgWidth = Math.min(maxWidth, pngImage.width);
      const imgHeight = (pngImage.height * imgWidth) / pngImage.width;
      const finalImgHeight = Math.min(imgHeight, maxHeight);
      const finalImgWidth = (imgWidth * finalImgHeight) / imgHeight;
      
      page.drawImage(pngImage, {
        x: 50,
        y: pageHeight - 100 - finalImgHeight,
        width: finalImgWidth,
        height: finalImgHeight
      });
      
      // Save and download
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${submissionData.studentName.replace(/\s+/g, '_')}_Feedback_Report_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error creating feedback PDF:', error);
      throw error;
    }
  };

  return (
    <div className="download-report-container">
      <button
            onClick={downloadCombinedReport}
            disabled={isDownloading}
            className="flex items-center gap-1 lg:gap-2 px-2 lg:px-3 py-2 text-xs lg:text-sm font-medium text-foreground bg-muted hover:bg-accent border border-border rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download Student Report"
        >
            <ArrowDownTrayIcon className="w-3 lg:w-4 h-3 lg:h-4" />
            <span className="hidden sm:inline">{isDownloading ? 'Generating...' : 'Download'}</span>
        </button>
      
      {/* {submissionData.pdfUrl && (
        <p className="report-info">
          This will combine the original answer sheet with detailed feedback.
        </p>
      )} */}
    </div>
  );
};

export default DownloadStudentReport;
